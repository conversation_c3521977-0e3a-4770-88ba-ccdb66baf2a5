
- جدول واحد يعرض **جميع التدخلات**، مع الأعمدة التالية:

| معرف التدخل | توقيت الخروج | نوع التدخل | الجهة المتصلة | نوع الاتصال | رقم الهاتف | الوسائل المرسلة | موقع الحادث | الحالة | الإجراءات |
|--------------|----------------|--------------|----------------|----------------|--------------|--------------------|----------------|----------|----------------|
| 00123         | 21:05              | انقلاب جرار  | الدرك الوطني      | هاتفية 📞         | 0660xxxxxx    | سيارة إسعاف 1        | مشتة البياضة | قيد التعرف | 🔍 التعرف ⬩ 🏁 إنهاء ⬩ 📄 التفاصيل |

> زر "📄 التفاصيل" يفتح صفحة مخصصة للتدخل حسب نوعه.
 عمود  الإجراءات
 عند حفظ البلاغ الاول يظهر زر عملية التعرف عند النقر عليه يتم استكمال صفحة عملية التعرف بعد الحظ يظهر زر إنهاء المهمة بعد الحفظ في الحالة العملية منتهية  زر التفاصيل عند الضغط عليه يآخذك مباشرة الى صفحة  التفاصيل الخاصة بالنوع التدخل 
---

## 📂 صفحة التفاصيل: عرض حسب نوع التدخل

   أ4أزرار رئيسة  زر يظهر لك الجدول الخاص بكل نوع  و تختفي الجداول الاخرى 

- 🚑 **إجلاء صحي**  
- 🚗 **حادث مرور**  
- 🏢 **حرائق البنايات**  
- 🌾 **حرائق المحاصيل الزراعية**
عمليات مختلفة سيتم انشاءه لاحقا لكن في  intervention_structure.md قم باضافةته لكي يفهم الوكيل التالي ما ينقص و عليه اكماله بعد عمل النموذج
---

### 📊 نموذج الجدول داخل كل نوع تدخل
هذا الجدول عبارة عن مثال اعتمد على النماذج في صفحة التدخلات اليومية 
| توقيت الخروج | نوع التدخل | الجهة المتصلة | نوع الاتصال | رقم الهاتف | الوسائل المرسلة | موقع الحادث | ساعة الوصول | عدد الضحايا | عدد الوفيات | أسماء المسعفين | أسماء الضحايا | الأعمار | الجنس | تفاصيل الإصابة | طبيعة الطريق أو الحريق | الخسائر | الأملاك المنقذة | الملاحظات | توقيت الانتهاء |
|--------------|--------------|----------------|----------------|--------------|--------------------|----------------|---------------|----------------|----------------|------------------|------------------|----------|--------|---------------------|----------------------------|-----------|--------------------|--------------|------------------|
| 21:05        | انقلاب جرار  | الدرك الوطني      | هاتفية 📞         | 0660xxxxxx    | سيارة إسعاف 1        | مشتة البياضة | 21:20          | 1              | 1              | إسماعيل، مراد         | إسحاق واصل            | 19 سنة  | ذكر   | كسر حوض – فقد وعي | طريق فلاحي ترابي           | تلفيات في الجرار | لا شيء             | [عرض التفاصيل] | 22:10            |

- عند الضغط على [عرض التفاصيل] تظهر نافذة جانبية فيها ملاحظة كاملة.  
- عند التصدير إلى Excel: تقسم كل 3 كلمات في سطر (داخل نفس الخلية).
عمود  الإجراءات
 عند حفظ البلاغ الاول يظهر زر عملية التعرف عند النقر عليه يتم استكمال صفحة عملية التعرف بعد الحظ يظهر زر إنهاء المهمة كلهم يأخذك لصفحة التدخلات اليومية  بعد الحفظ في الحالة العملية منتهية   
---
---

## 📄 محتوى النماذج (مستوحات  من صفحة التدخلات اليومية )

### 🔹 البلاغ الأولي – مشترك بين الأنواع

- 🕒 توقيت الخروج  
- 🧭 نوع التدخل  
- 🚒 الوسائل المرسلة  
- 👤 الجهة المتصلة  
- ☎️ رقم الهاتف  
- نوع الاتصال: (هاتفي، مباشر، راديو، طلب دعم...)  
- 📝 ملاحظة أولية  
- 🟡 الحالة = `قيد التعرف`

---


🟡 **الحالة الحالية:** `قيد التعرف`
#### 🚑 إجلاء:
### 2️⃣ عملية التعرف

#### 🔁 خطوات:

- 🕒 ساعة الوصول
- 📋 طبيعة التدخل حسب ما تم اختياره في البلاغ
- 📍 الموقع: داخل المنزل / خارج المنزل

#### 🧮 إحصاء الضحايا:

- عدد المسعفين
- عدد الوفيات
- ملاحظة عن الخسائر المادية (اختياري)

#### 📂 قائمة الأنواع حسب طبيعة التدخل:

##### الاختناق:
- بالغاز الطبيعي أو البوتان
- غاز CO
- انسداد المجاري التنفسية
- الأماكن المغلقة
- ➕ إضافة نوع جديد (للإدمن)

##### التسممات:
- مواد غذائية
- أدوية
- منظفات
- لسعات أو عضّات
- أخرى
- ➕ إضافة نوع

##### الحروق:
- ألسنة اللهب
- سوائل ساخنة
- مواد كيميائية/مشعة
- صعقات كهربائية

##### الانفجارات:
- غاز البوتان / الغاز الطبيعي
- الأجهزة الكهرومنزلية / أجهزة التدفئة

##### إجلاء المرضى:
- جرحى، فاقدي الوعي، اختناقات، تسممات، سقوط، شنق...

##### الغرقى:
- مسطحات مائية، سدود، أودية، شواطئ، أماكن أخرى

---

#### 🚨 طلب الدعم:

- شكراً، الوضع تحت السيطرة
- نعم وسيلة إضافية
- نعم وحدة مجاورة
  - 🔔 إرسال إشارة صوتية إلى مركز التنسيق الولائي
- نعم فريق متخصص:
  - فرقة الغطاسين
  - التدخل في الأماكن الوعرة
  - فرقة السينوتقنية
  - ➕ إضافة فرقة
#### 🚗 حادث مرور:
- - 🕒 ساعة الوصول
- 🧾 اختيار نوع الحادث حسب البلاغ
- 🗂️ الأنواع: اعتمد على النموذج عملية التعرف 

#### ضحايا مصدومة بالمركبات:
- سيارة، شاحنة، حافلة، دراجة نارية، أخرى

#### ضحايا تصادم المركبات:
- سيارة × سيارة، سيارة × شاحنة، ...

#### ضحايا إنقلاب:
- سيارة، شاحنة، ...

#### ضحايا مصدومة بالقطار:
- سيارة، شخص، شاحنة...

#### حوادث أخرى:
- سقوط من مركبة
- حريق مركبة
- انفجار مركبة

📝 ملاحظة حول الخسائر المادية


- الطلب دعم وسيلة أخرى أو وحدة

#### 🏢 حرائق بنايات:

### 🕓 ساعة الوصول: `[اختيار التوقيت]`

### 🏚️ طبيعة الحريق:
**(تُعرض حسب نوع التدخل المحدد):**

#### 🏠 حرائق البنايات السكنية:
- شقة
- منزل فردي
- عمارة
- مجمع سكني

#### 🏢 حرائق المؤسسات المصنفة:
- مصنع
- ورشة
- مدجنة
- مخزن

#### 🏛️ حرائق الأماكن المستقبلة للجمهور:
- مدرسة
- مستشفى
- مسجد
- قاعة حفلات
- مركز تجاري

#### 🚗 حرائق المركبات:
- سيارة
- شاحنة
- حافلة
- دراجة نارية
- مركبة أخرى

### 📍 الموقع:
- داخل البناية
- خارج المبنى
- طابق معين: `[اختياري]`
- غرفة محددة: `[اختياري]`
- مكان مهدد بالانتشار

### 🔥 انتشار الحريق:
- ✅ عدد نقاط الاشتعال: `[رقم]`
- 💨 جهة الرياح (إن وُجد): `[نص]`
- 🌬️ سرعة الرياح (إن وُجدت): `[رقم كم/سا]`
- 👥 تهديد السكان: نعم / لا
- 📍 هل تم إجلاء السكان؟ نعم / لا
- 🧯 ماهية المساعدات المقدّمة للسكان: `[نص حر]`

### 🚨 طلب الدعم:
- شكراً، الوضع تحت السيطرة
- نعم وسيلة إضافية
- نعم وحدة مجاورة → يشرف عليها مركز التنسيق الولائي
- نعم فرق متخصصة (غطس، إنقاذ في الأماكن الوعرة…)

||| # 🔥  حريق محاصيل زراعية

🕘 **ساعة الوصول:** [HH:MM]
- 🔥 **نوع الحريق:** 
  - قمح واقف
  - حصيدة
  - شعير
  - حزم تبن
  - غابة / أحراش
  - أكياس شعير / قمح
  - أشجار مثمرة (بالتعداد)
  - خلايا نحل (بالتعداد)

- 📏 **انتشار الحريق:**
  - 🔥 عدد البؤر (الموقد): [عدد]
  - 🌬️ اتجاه الرياح: [اتجاه]
  - 💨 سرعة الرياح: [كم/سا]
  - 🏘️ تهديد للسكان: نعم / لا
  - 🧭 مكان إجلاء السكان (إن وُجد): [اسم المكان]

- 🚒 **عدد الأعوان المتدخلين:** [عدد]
- 👨‍⚕️ **عدد المسعفين:** [عدد]
- ⚰️ **عدد الوفيات:** [عدد]
- 👨‍👩‍👧‍👦 **عدد العائلات المتأثرة:** [عدد]
- 👮‍♂️ **الجهات الحاضرة:** درك / شرطة / حراس غابات / سلطات محلية...

- 📤 **طلب دعم:** 
  - ✅ شكراً، الوضع تحت السيطرة
  - 🚒 نعم وسيلة إضافية
  - 🔁 نعم وحدة مجاورة → إشعار مركز التنسيق الولائي
  - 🛠️ نعم فرق متخصصة:
    - 👨‍🚒 فرقة الغطس
    - 🧗 فرقة الأماكن الوعرة
    - 🐕 فرقة السينوتقنية
بعد الحفظ تحفظ في الجدول الرئسي و الجدول الخاص بنوع التدخل 

🟠 الحالة = `عملية تدخل`


---

### 🔹إنهاء المهمة – حسب نوع التدخل 
🚑 إجلاء:

- ⏱️ ساعة نهاية التدخل
- 🧮 الإحصائيات النهائية:
  - عدد المسعفين: إدخال الاسم، السن، الجنس
  - عدد الوفيات: الاسم، السن، الجنس
- عدد التدخلات = الوسائل الأساسية + وسائل الدعم
## 🚗 حوادث المرور
- ⏱️ ساعة نهاية التدخل
- 🧮 الإحصائيات النهائية:
  - عدد الضحايا → تظهر حقول: الاسم، السن، الجنس، الحالة (سائق / راكب / مشاة...)
  - عدد الوفيات → الحقول نفسها

- 🚧 نوع الطريق:
  - الطريق السيار
  - الطريق الوطني
  - الطريق الولائي
  - الطريق البلدي
  - طرق أخرى

# 🔥  حرائق البنايات والمؤسسات
### 🕙 ساعة انتهاء المهمة: `[اختيار الوقت]`

### 👥 الإحصائيات النهائية:

- عدد الأعوان المتدخلين: `[يُجلب من التعداد الآلي]`
- عدد العائلات المتضررة (إن وُجدت): `[رقم]`
- عدد المسعفين: `[رقم]` → يتم إدخال تفاصيل كل مسعف:
  - الاسم واللقب
  - السن
  - الجنس
- عدد الوفيات: `[رقم]` → يتم إدخال:
  - الاسم واللقب
  - السن
  - الجنس
  - التصنيف (ساكن، موظف، زائر…)

### 🏚️ الخسائر:
- يتم إدخال وصف نصي حر:  
  `مثال: احتراق كلي لمحل تجاري يحتوي على مواد تجميل – احتراق جزئي لطابق علوي – تلف تجهيزات إلكترونية – احتراق مخزون مواد بلاستيكية...`

### 🟢 الأملاك المنقذة:
- وصف نصي حر:  
  `مثال: إنقاذ 4 أسطوانات غاز – منع امتداد الحريق إلى شقق الطابق الثالث – حماية محطة كهرباء مجاورة...`

### 📝 ملاحظات ختامية:
`[نص حر]`

# 🔥  حريق محاصيل زراعية


- ⏱️ **ساعة نهاية التدخل:** [HH:MM]
- 📋 **الخسائر:**

### 🔻 حسب المساحة:
- 🌾 قمح واقف: [هكتار]
- 🌾 حصيدة: [هكتار]
- 🌿 غابة/أحراش: [هكتار / آر / متر مربع]
- 🌾 شعير: [هكتار]

### 🔻 حسب العدد:
- 📦 حزم تبن: [عدد]
- 🏷️ أكياس قمح/شعير: [عدد]
- 🌳 أشجار مثمرة: [عدد]
- 🐝 خلايا نحل: [عدد]

### 🟢 الأملاك المنقذة:
- 🌾 مساحة منقذة: [هكتار]
- 📦 عدد حزم التبن المنقذة: [عدد]
- 🚜 ممتلكات أو آلات تم إنقاذها: [وصف]

- 👨‍⚕️ **تفصيل الضحايا (إن وُجد):**

#### إحصائيات الضحايا:
- **عدد الضحايا**: مع الحقول التالية لكل ضحية
  - الاسم الكامل
  - السن
  - الجنس



عند الحفظ تسجل في الجدول المشترك للاعمدة المشتركة و تسجل غي صفحة الجدول الخاص بنوع التدخل 
🟢 **الحالة النهائية:** `منتهية`
---



🟢 الحالة = `منتهية`

---

## 📦 التحميل والتصدير

- 📥 **تحميل Excel**:
او كل الجداول مع بعض او حسب النوع 

  - لكل نوع تدخل جدول منفصل
  - تنسيق wrap text داخل الخلايا الطويلة
من اليمين لليسار 
- 📄 **تحميل PDF**:
  - تقرير مفصل لكل تدخل
او كل الجداول مع بعض او حسب النوع 
---

## ⚙️ المزايا البرمجية:

- كل التدخلات مرتبطة بـ `intervention_id`
- كل مرحلة محفوظة كـ `intervention_stage`
- كل سجل يحتوي على `created_by_unit`
- يتم جلب أسماء الأعوان تلقائيًا من توزيع التعداد الصباحي
- يتم إرسال إشعار في حال تأخر إدخال "إنهاء المهمة"
