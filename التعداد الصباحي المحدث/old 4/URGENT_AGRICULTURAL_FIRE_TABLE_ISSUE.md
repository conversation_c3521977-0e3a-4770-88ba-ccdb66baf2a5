# 🚨 مشكلة عاجلة: جدول حريق المحاصيل الزراعية - 29 يوليو 2025

## 📋 **المشكلة الحالية:**

المستخدم قام بإستعادة (restore) جميع التغييرات السابقة وطلب إصلاح جدول حريق المحاصيل الزراعية ليعرض **47 عمود** بالترتيب الصحيح.

## 🎯 **المطلوب من الوكيل التالي:**

### **اقرأ هذه الملفات أولاً:**
1. `@/Users/<USER>/Documents/Copy_Secure_DPC_DZ/DPC_DZ/table_structure_colums.md`
2. `@/Users/<USER>/Documents/Copy_Secure_DPC_DZ/DPC_DZ/URGENT_FORMS_TABLES_SYNC_ISSUE.md`
3. `@/Users/<USER>/Documents/Copy_Secure_DPC_DZ/DPC_DZ/جدول_المحاصيل_الزراعية.md`

### **الحقول الـ47 المطلوبة بالترتيب الصحيح:**

1. معرف التدخل ✅
2. توقيت الخروج ✅
3. نوع التدخل ✅
4. مكان التدخل ✅
5. الوسائل المرسلة ✅
6. الجهة المتصلة ✅
7. نوع الاتصال ✅
8. رقم الهاتف ✅
9. اسم المتصل ✅
10. ملاحظة إضافية ✅
11. ساعة الوصول ✅
12. ملاحظة عن الخسائر المادية (اختياري) ✅
13. نوع المحصول المحترق ✅
14. عدد البؤر (الموقد) ✅
15. اتجاه الرياح ✅
16. سرعة الرياح (كم/سا) ✅
17. تهديد للسكان ✅
18. مكان إجلاء السكان (إن وُجد) ✅
19. عدد العائلات المتأثرة ✅
20. الجهات الحاضرة ✅
21. طلب الدعم ✅
22. ساعة نهاية التدخل ✅
23. مدة التدخل الإجمالية ✅
24. عدد الضحايا ✅
25. أسماء الضحايا (كل اسم في سطر) ✅
26. أعمار الضحايا (مقابل كل اسم) ✅
27. جنس الضحايا (مقابل كل اسم) ✅
28. الحالة ✅
29. عدد الوفيات ✅
30. أسماء الوفيات (كل اسم في سطر) ✅
31. أعمار الوفيات (مقابل كل اسم) ✅
32. جنس الوفيات (مقابل كل اسم) ✅
33. عدد التدخلات (يُحسب آليًا) ✅
34. قمح واقف (هكتار) ✅
35. حصيدة (هكتار) ✅
36. شعير (هكتار) ✅
37. غابة/أحراش (هكتار) ✅
38. حزم تبن (عدد) ✅
39. أكياس قمح/شعير (عدد) ✅
40. أشجار مثمرة (عدد) ✅
41. خلايا نحل (عدد) ✅
42. مساحة منقذة (هكتار) ✅
43. حزم التبن المنقذة (عدد) ✅
44. ممتلكات أو آلات تم إنقاذها ✅
45. ملاحظات ختامية ✅
46. الحالة ✅
47. الإجراءات ✅

## 🔍 **المشاكل المحتملة التي يجب فحصها:**

### **1. مشكلة في HTML الجدول:**
- تحقق من عدد الأعمدة في `dpcdz/templates/coordination_center/intervention_details.html`
- تأكد من وجود جميع الـ47 عمود في جدول حريق المحاصيل
- تحقق من `colspan` في حالة عدم وجود بيانات

### **2. مشكلة في JavaScript العرض:**
- تحقق من دالة `createTableRow` للنوع `crop`
- تأكد من أن JavaScript يعرض جميع الـ47 حقل
- تحقق من ترتيب الحقول

### **3. مشكلة في API جلب البيانات:**
- تحقق من دالة `get_interventions_by_type` في `views.py`
- تأكد من جلب البيانات من `AgriculturalFireDetail`
- تحقق من إرسال جميع الحقول في JSON response

### **4. مشكلة في حفظ البيانات:**
- تحقق من النماذج في `daily_interventions.html`
- تأكد من وجود جميع العناصر المطلوبة
- تحقق من دالة `saveAgriculturalFireDetails`

### **5. مشكلة في النموذج:**
- تحقق من `AgriculturalFireDetail` في `models.py`
- تأكد من وجود جميع الحقول المطلوبة

## ⚠️ **تحذيرات مهمة:**

1. **لا تلمس الأنواع الأخرى**: الإجلاء الصحي وحوادث المرور وحرائق البنايات تعمل بشكل صحيح
2. **ركز فقط على حريق المحاصيل الزراعية**
3. **تأكد من الترتيب الصحيح للأعمدة**
4. **تأكد من عدد الأعمدة = 47**

## 🎯 **الهدف النهائي:**

جعل جدول حريق المحاصيل الزراعية يعرض جميع الـ47 عمود بالترتيب الصحيح مع البيانات الصحيحة من النماذج المتخصصة.

## 📞 **رسالة للوكيل التالي:**

**عزيزي الوكيل التالي،**

المستخدم استعاد جميع التغييرات السابقة وطلب إصلاح جدول حريق المحاصيل الزراعية ليعرض 47 عمود بالترتيب الصحيح.

**ابدأ بقراءة الملفات المذكورة أعلاه، ثم:**
1. تحقق من HTML الجدول
2. تحقق من JavaScript العرض
3. تحقق من API جلب البيانات
4. تحقق من حفظ البيانات
5. اختبر النظام بالكامل

**🔥 ابدأ العمل فوراً - المستخدم ينتظر!**
