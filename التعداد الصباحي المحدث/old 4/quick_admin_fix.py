#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح سريع لمشاكل admin.py
تشغيل هذا الملف سيصلح المشاكل الأساسية في admin.py
"""

import os
import re

def fix_admin_file():
    """إصلاح ملف admin.py بإزالة الحقول غير الموجودة"""
    
    admin_file_path = "dpcdz/home/<USER>"
    
    if not os.path.exists(admin_file_path):
        print(f"❌ ملف {admin_file_path} غير موجود")
        return
    
    # قراءة الملف
    with open(admin_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # الحقول التي يجب إزالتها من list_display
    fields_to_remove_from_display = [
        'fire_sources_count', 'population_threat', 'affected_families_count',
        'fire_nature', 'fire_location', 'updated_by', 'updated_at', 'date',
        'is_present', 'category', 'is_active', 'created_at', 'full_name',
        'is_primary', 'assigned_at', 'intervention_nature', 'location_type',
        'support_request', 'active_shift', 'from_shift', 'to_shift', 'status',
        'requested_by', 'priority', 'assigned_date', 'assigned_by',
        'accident_type', 'item_name', 'transfer_type', 'transfer_date',
        'transferred_by', 'assignment_date', 'current_intervention',
        'readiness_score', 'is_automatically_ready', 'is_manually_confirmed',
        'years_of_service', 'age'
    ]
    
    # الحقول التي يجب إزالتها من list_filter
    fields_to_remove_from_filter = fields_to_remove_from_display + [
        'contact_source', 'unit'
    ]
    
    # الحقول التي يجب إزالتها من readonly_fields
    fields_to_remove_from_readonly = [
        'updated_at', 'created_at'
    ]
    
    # الحقول التي يجب إزالتها من ordering
    fields_to_remove_from_ordering = [
        'date', 'category', 'priority', 'assignment_date'
    ]
    
    # إصلاح list_display
    for field in fields_to_remove_from_display:
        # البحث عن list_display وإزالة الحقل
        pattern = rf"list_display\s*=\s*\[(.*?)\]"
        matches = re.finditer(pattern, content, re.DOTALL)
        for match in matches:
            display_content = match.group(1)
            if f"'{field}'" in display_content or f'"{field}"' in display_content:
                # إزالة الحقل من القائمة
                new_display = re.sub(rf"['\"]?{field}['\"]?\s*,?\s*", "", display_content)
                new_display = re.sub(r',\s*,', ',', new_display)  # إزالة الفواصل المتتالية
                new_display = re.sub(r',\s*\]', ']', new_display)  # إزالة الفاصلة قبل الإغلاق
                content = content.replace(match.group(0), f"list_display = [{new_display}]")
    
    # إصلاح list_filter
    for field in fields_to_remove_from_filter:
        pattern = rf"list_filter\s*=\s*\[(.*?)\]"
        matches = re.finditer(pattern, content, re.DOTALL)
        for match in matches:
            filter_content = match.group(1)
            if f"'{field}'" in filter_content or f'"{field}"' in filter_content:
                new_filter = re.sub(rf"['\"]?{field}['\"]?\s*,?\s*", "", filter_content)
                new_filter = re.sub(r',\s*,', ',', new_filter)
                new_filter = re.sub(r',\s*\]', ']', new_filter)
                content = content.replace(match.group(0), f"list_filter = [{new_filter}]")
    
    # إصلاح readonly_fields
    for field in fields_to_remove_from_readonly:
        pattern = rf"readonly_fields\s*=\s*\[(.*?)\]"
        matches = re.finditer(pattern, content, re.DOTALL)
        for match in matches:
            readonly_content = match.group(1)
            if f"'{field}'" in readonly_content or f'"{field}"' in readonly_content:
                new_readonly = re.sub(rf"['\"]?{field}['\"]?\s*,?\s*", "", readonly_content)
                new_readonly = re.sub(r',\s*,', ',', new_readonly)
                new_readonly = re.sub(r',\s*\]', ']', new_readonly)
                content = content.replace(match.group(0), f"readonly_fields = [{new_readonly}]")
    
    # إصلاح ordering
    for field in fields_to_remove_from_ordering:
        pattern = rf"ordering\s*=\s*\[(.*?)\]"
        matches = re.finditer(pattern, content, re.DOTALL)
        for match in matches:
            ordering_content = match.group(1)
            if f"'{field}'" in ordering_content or f'"{field}"' in ordering_content:
                new_ordering = re.sub(rf"['\"]?{field}['\"]?\s*,?\s*", "", ordering_content)
                new_ordering = re.sub(r',\s*,', ',', new_ordering)
                new_ordering = re.sub(r',\s*\]', ']', new_ordering)
                content = content.replace(match.group(0), f"ordering = [{new_ordering}]")
    
    # إضافة تعليق في بداية الملف
    header_comment = '''# -*- coding: utf-8 -*-
"""
ملف Admin محدث - تم إصلاح المشاكل تلقائياً
تم إزالة الحقول غير الموجودة في النماذج المقسمة
"""

'''
    
    if not content.startswith('# -*- coding: utf-8 -*-'):
        content = header_comment + content
    
    # كتابة الملف المحدث
    with open(admin_file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ تم إصلاح ملف admin.py بنجاح")
    print("📝 تم إزالة الحقول غير الموجودة من:")
    print("   - list_display")
    print("   - list_filter") 
    print("   - readonly_fields")
    print("   - ordering")
    print("\n🚀 يمكنك الآن تشغيل: python manage.py runserver")


def create_backup():
    """إنشاء نسخة احتياطية من admin.py"""
    admin_file_path = "dpcdz/home/<USER>"
    backup_path = "dpcdz/home/<USER>"
    
    if os.path.exists(admin_file_path):
        with open(admin_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")


if __name__ == "__main__":
    print("🔧 بدء إصلاح مشاكل admin.py...")
    
    # إنشاء نسخة احتياطية
    create_backup()
    
    # إصلاح الملف
    fix_admin_file()
    
    print("\n✨ تم الانتهاء من الإصلاح!")
    print("💡 نصائح:")
    print("   1. تشغيل: python manage.py check")
    print("   2. إذا كانت هناك مشاكل أخرى، راجع Views_New_structure.md")
    print("   3. تشغيل: python manage.py runserver")
