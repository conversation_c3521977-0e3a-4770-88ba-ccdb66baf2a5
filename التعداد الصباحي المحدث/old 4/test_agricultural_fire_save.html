<!DOCTYPE html>
<html>
<head>
    <title>اختبار حفظ بيانات حريق المحاصيل</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>اختبار حفظ بيانات حريق المحاصيل</h1>
    
    <div>
        <h3>محاكاة النماذج:</h3>
        <input type="text" id="fire-nature" value="قمح واقف" placeholder="طبيعة الحريق">
        <input type="number" id="ignition-points" value="3" placeholder="عدد نقاط الاشتعال">
        <input type="text" id="wind-direction" value="شمال" placeholder="اتجاه الرياح">
        <input type="number" id="wind-speed" value="15" placeholder="سرعة الرياح">
        <select id="agricultural-threat-residents">
            <option value="yes" selected>نعم</option>
            <option value="no">لا</option>
        </select>
        <input type="text" id="agricultural-evacuation-place" value="مدرسة القرية" placeholder="مكان الإجلاء">
        <input type="number" id="agricultural-affected-families" value="5" placeholder="عدد العائلات المتأثرة">
        <input type="text" id="support-request" value="وحدة مجاورة" placeholder="طلب الدعم">
        
        <br><br>
        <button onclick="testSaveFunction()">اختبار دالة الحفظ</button>
        <button onclick="testAPICall()">اختبار API مباشرة</button>
    </div>
    
    <div id="result"></div>

    <script>
        // محاكاة window.currentInterventionId
        window.currentInterventionId = 71;
        
        // محاكاة دالة getCsrfToken
        function getCsrfToken() {
            return 'test-csrf-token';
        }
        
        // نسخة من دالة saveAgriculturalFireDetails
        async function saveAgriculturalFireDetails() {
            try {
                const detailsData = {
                    intervention_id: window.currentInterventionId,
                    fire_type: document.getElementById('fire-nature')?.value || '',
                    fire_sources_count: document.getElementById('ignition-points')?.value || 0,
                    wind_direction: document.getElementById('wind-direction')?.value || '',
                    wind_speed: document.getElementById('wind-speed')?.value || 0,
                    population_threat: document.getElementById('agricultural-threat-residents')?.value === 'yes',
                    evacuation_location: document.getElementById('agricultural-evacuation-place')?.value || '',
                    affected_families_count: document.getElementById('agricultural-affected-families')?.value || 0,
                    support_request: document.getElementById('support-request')?.value || '',
                };

                console.log('بيانات حريق المحاصيل المرسلة:', detailsData);
                
                document.getElementById('result').innerHTML = `
                    <h3>البيانات المجمعة:</h3>
                    <pre>${JSON.stringify(detailsData, null, 2)}</pre>
                `;
                
                return true;
            } catch (error) {
                console.error('خطأ في حفظ تفاصيل حريق المحاصيل:', error);
                return false;
            }
        }
        
        function testSaveFunction() {
            console.log('اختبار دالة الحفظ...');
            saveAgriculturalFireDetails();
        }
        
        async function testAPICall() {
            const detailsData = {
                intervention_id: 71,
                fire_type: document.getElementById('fire-nature')?.value || '',
                fire_sources_count: document.getElementById('ignition-points')?.value || 0,
                wind_direction: document.getElementById('wind-direction')?.value || '',
                wind_speed: document.getElementById('wind-speed')?.value || 0,
                population_threat: document.getElementById('agricultural-threat-residents')?.value === 'yes',
                evacuation_location: document.getElementById('agricultural-evacuation-place')?.value || '',
                affected_families_count: document.getElementById('agricultural-affected-families')?.value || 0,
                support_request: document.getElementById('support-request')?.value || '',
            };
            
            console.log('اختبار API call مع البيانات:', detailsData);
            
            try {
                const response = await fetch('http://127.0.0.1:8000/api/interventions/save-agricultural-fire-details/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': 'test-csrf-token'
                    },
                    body: JSON.stringify(detailsData)
                });

                const result = await response.json();
                console.log('نتيجة API:', result);
                
                document.getElementById('result').innerHTML = `
                    <h3>نتيجة API:</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('خطأ في API call:', error);
                document.getElementById('result').innerHTML = `
                    <h3>خطأ في API:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }
    </script>
</body>
</html>
