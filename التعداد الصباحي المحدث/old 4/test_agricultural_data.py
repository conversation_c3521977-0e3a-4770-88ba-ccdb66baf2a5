#!/usr/bin/env python3
"""
اختبار سريع لفحص بيانات حريق المحاصيل الزراعية
"""

import os
import sys
import django

# إعداد Django
sys.path.append('/Users/<USER>/Documents/Copy_Secure_DPC_DZ/DPC_DZ/dpcdz')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dpcdz.settings')
django.setup()

from home.models import DailyIntervention, AgriculturalFireDetail, InterventionUnit
from datetime import date

def test_agricultural_fire_data():
    print("=== اختبار بيانات حريق المحاصيل الزراعية ===")
    
    # 1. فحص التدخلات الموجودة
    interventions = DailyIntervention.objects.all()
    print(f"إجمالي التدخلات: {interventions.count()}")
    
    # 2. فحص تدخلات حريق المحاصيل
    crop_interventions = DailyIntervention.objects.filter(
        intervention_type__icontains='محاصيل'
    )
    print(f"تدخلات حريق المحاصيل: {crop_interventions.count()}")
    
    # 3. فحص التدخلات بأنواع مختلفة
    fire_types = ['agricultural-fire', 'حريق محاصيل', 'crop']
    for fire_type in fire_types:
        count = DailyIntervention.objects.filter(
            intervention_type__icontains=fire_type
        ).count()
        print(f"تدخلات نوع '{fire_type}': {count}")
    
    # 4. فحص النماذج المتخصصة
    agricultural_details = AgriculturalFireDetail.objects.all()
    print(f"تفاصيل حريق المحاصيل: {agricultural_details.count()}")
    
    # 5. إنشاء تدخل تجريبي إذا لم يكن موجود
    if crop_interventions.count() == 0:
        print("\n=== إنشاء تدخل تجريبي ===")
        
        # الحصول على وحدة
        unit = InterventionUnit.objects.first()
        if not unit:
            print("خطأ: لا توجد وحدات في النظام")
            return
        
        # إنشاء تدخل
        intervention = DailyIntervention.objects.create(
            unit=unit,
            intervention_type='حريق محاصيل زراعية',
            location='منطقة تجريبية',
            date=date.today(),
            contact_source='citizen',
            contact_type='phone'
        )
        print(f"تم إنشاء تدخل رقم: {intervention.id}")
        
        # إنشاء تفاصيل متخصصة
        detail = AgriculturalFireDetail.objects.create(
            intervention=intervention,
            fire_type='standing_wheat',
            fire_sources_count=3,
            wind_direction='شمال',
            wind_speed=15.5,
            population_threat=True,
            evacuation_location='المدرسة المحلية',
            intervening_agents_count=8,
            affected_families_count=5,
            standing_wheat_area=2.5,
            harvest_area=1.0,
            barley_area=0.5,
            straw_bales_count=50,
            grain_bags_count=20,
            fruit_trees_count=10,
            beehives_count=3,
            saved_area=1.5,
            saved_straw_bales=30,
            saved_equipment='جرار زراعي، آلة حصاد',
            final_notes='تدخل تجريبي لاختبار النظام'
        )
        print(f"تم إنشاء تفاصيل متخصصة رقم: {detail.id}")
    
    # 6. فحص البيانات المحفوظة
    print("\n=== فحص البيانات المحفوظة ===")
    for intervention in DailyIntervention.objects.filter(
        intervention_type__icontains='محاصيل'
    )[:3]:
        print(f"\nتدخل رقم: {intervention.id}")
        print(f"النوع: {intervention.intervention_type}")
        print(f"التاريخ: {intervention.date}")
        
        if hasattr(intervention, 'agricultural_fire_detail'):
            detail = intervention.agricultural_fire_detail
            print(f"نوع الحريق: {detail.fire_type}")
            print(f"عدد البؤر: {detail.fire_sources_count}")
            print(f"اتجاه الرياح: {detail.wind_direction}")
            print(f"سرعة الرياح: {detail.wind_speed}")
            print(f"قمح واقف: {detail.standing_wheat_area} هكتار")
            print(f"حصيدة: {detail.harvest_area} هكتار")
            print(f"حزم تبن: {detail.straw_bales_count}")
            print(f"الأملاك المنقذة: {detail.saved_equipment}")
        else:
            print("لا توجد تفاصيل متخصصة")

if __name__ == '__main__':
    test_agricultural_fire_data()
